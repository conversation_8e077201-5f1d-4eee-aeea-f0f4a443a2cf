@echo off
echo 🧪 Testing SPRINT - AI Sidebar Navigation
echo =========================================

echo 📁 Starting frontend...
cd frontend

echo 🚀 Starting development server...
echo.
echo 🌐 Frontend starting at: http://localhost:3000
echo.
echo 📋 Test Sidebar Navigation:
echo    1. Go to: http://localhost:3000/lessons/level-1
echo    2. Click on "Introduction to Computers" in sidebar
echo    3. Should show sub-lessons (What is a Computer?, etc.)
echo    4. Click on "What is a Computer?" 
echo    5. Should load the lesson content
echo    6. Click "Mark as Read" button
echo    7. Should automatically go to next lesson
echo    8. Test "Back to Topics" button
echo    9. Test "Back to Dashboard" button
echo.
echo 🎯 Expected Behavior:
echo    ✅ Sidebar shows major topics initially
echo    ✅ Clicking major topic shows sub-lessons
echo    ✅ Clicking sub-lesson loads content
echo    ✅ Mark as Read advances to next lesson
echo    ✅ Back buttons work properly
echo    ✅ Navigation flows smoothly
echo.
echo 🔍 Check for:
echo    ❌ No sidebar clicks working
echo    ❌ Mark as Read not advancing
echo    ❌ Back to Dashboard showing alert
echo    ❌ Lessons not loading
echo.

npm run dev
