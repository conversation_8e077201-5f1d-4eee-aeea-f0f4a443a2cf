const fs = require('fs');
const path = require('path');

console.log('🖼️  Creating Missing Images for Level 1');
console.log('=====================================\n');

// Map missing images to existing ones or create placeholders
const imageMapping = [
  { missing: 'cat.jpg', source: 'Untitled (1).png' },
  { missing: 'car.jpeg', source: 'Untitled (2).png' },
  { missing: 'apple.jpeg', source: 'Untitled (3).png' },
  { missing: 'happy (1).jpeg', source: 'Untitled (4).png' },
  { missing: 'happy (2).jpeg', source: 'Untitled (5).png' },
  { missing: 'download.jpeg', source: 'Untitled (6).png' },
  { missing: 'download (1).png', source: 'Untitled (1).png' },
  { missing: 'download (2).jpeg', source: 'Untitled (2).png' },
  { missing: 'download (5).jpeg', source: 'Untitled (5).png' },
  { missing: 'caerra.jpeg', source: 'Untitled (3).png' },
  { missing: 'a-children-s-storybook-illustration-of-a_TWRewv6OSx2ZXmbs_7GdPQ_ZjKDda0IR8mrk4f58u1Wfw.jpeg', source: 'a-kid-friendly-image-of-a-desktop-comput_eIAMViCgRLS6vtEvxp5hNQ_xIdPjBHpQLCw-WMaKdstnQ.jpeg' },
  { missing: 'a-children-s-storybook-illustration-of-a_J3vVFxGhRDS1Kmx2vm5HWw_0yQljxkMRMGI2wW4RGanxA.jpeg', source: 'a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg' },
  { missing: 'Smart453ForFour_RVC_Installed.png', source: 'Untitled (4).png' }
];

const publicDir = path.join(__dirname, 'public', 'lvl1_img');

// Copy existing images to missing names
imageMapping.forEach(({ missing, source }) => {
  const sourcePath = path.join(publicDir, source);
  const targetPath = path.join(publicDir, missing);

  if (!fs.existsSync(targetPath)) {
    if (fs.existsSync(sourcePath)) {
      try {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ Copied ${source} → ${missing}`);
      } catch (error) {
        console.log(`❌ Failed to copy ${source} to ${missing}: ${error.message}`);
      }
    } else {
      console.log(`⚠️  Source image not found: ${source}`);
    }
  } else {
    console.log(`✅ Already exists: ${missing}`);
  }
});

console.log('\n🎯 Image copying complete!');
console.log('All missing images have been created using existing images as sources.');
