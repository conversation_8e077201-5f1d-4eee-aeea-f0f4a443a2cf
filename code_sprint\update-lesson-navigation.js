const fs = require('fs');
const path = require('path');

console.log('🔧 Updating Lesson Navigation');
console.log('=============================\n');

// Define the lesson files to update
const lessonFiles = [
  'frontend/src/pages/lessons/level1/1.2HowComputersWork.js',
  'frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js',
  'frontend/src/pages/lessons/level1/1.4TypesOfComputers.js',
  'frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js',
  'frontend/src/pages/lessons/level1/1.6OperatingSystem.js',
  'frontend/src/pages/lessons/level1/2.1WhatIsScratch.js',
  'frontend/src/pages/lessons/level1/3.1WhatIsIoT.js',
  'frontend/src/pages/lessons/level1/3.2HowIoTWorks.js',
  'frontend/src/pages/lessons/level1/3.3IoTExamples.js',
  'frontend/src/pages/lessons/level1/3.4IoTBenefits.js',
  'frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js',
  'frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js',
  'frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js',
  'frontend/src/pages/lessons/level1/5.1Summary.js',
  'frontend/src/pages/lessons/level1/5.3CompletionMessage.js'
];

function updateLessonFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let updated = false;
    
    // Update component function signature
    const componentRegex = /const\s+(\w+)\s*=\s*\(\s*\)\s*=>/;
    if (componentRegex.test(content)) {
      content = content.replace(componentRegex, 'const $1 = ({ onNext }) =>');
      updated = true;
      console.log(`   ✅ Updated component signature`);
    }
    
    // Update MarkAsReadButton to include onNext prop
    const markAsReadRegex = /(<MarkAsReadButton[^>]*?)(\s*\/?>)/;
    if (markAsReadRegex.test(content) && !content.includes('onNext={onNext}')) {
      content = content.replace(markAsReadRegex, (match, opening, closing) => {
        if (!opening.includes('onNext=')) {
          return opening + '\n          onNext={onNext}' + closing;
        }
        return match;
      });
      updated = true;
      console.log(`   ✅ Added onNext prop to MarkAsReadButton`);
    }
    
    if (updated) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error updating ${filePath}: ${error.message}`);
    return false;
  }
}

// Main execution
function main() {
  console.log('🚀 Starting lesson navigation updates...\n');
  
  let updatedCount = 0;
  let totalFiles = 0;
  
  lessonFiles.forEach(filePath => {
    totalFiles++;
    console.log(`📝 Processing: ${filePath}`);
    if (updateLessonFile(filePath)) {
      updatedCount++;
    }
    console.log('');
  });
  
  console.log('📊 Update Summary:');
  console.log('==================');
  console.log(`Files processed: ${totalFiles}`);
  console.log(`Files updated: ${updatedCount}`);
  console.log(`Files unchanged: ${totalFiles - updatedCount}`);
  
  if (updatedCount > 0) {
    console.log('\n🎉 Lesson navigation updates completed!');
    console.log('\n🚀 Next steps:');
    console.log('1. Run: test-sidebar-navigation.bat');
    console.log('2. Test sidebar navigation');
    console.log('3. Test Mark as Read progression');
  } else {
    console.log('\nℹ️  No files needed updating.');
  }
}

// Run the script
main();
