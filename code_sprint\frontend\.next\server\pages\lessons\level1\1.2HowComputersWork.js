/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/lessons/level1/1.2HowComputersWork";
exports.ids = ["pages/lessons/level1/1.2HowComputersWork"];
exports.modules = {

/***/ "(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.2HowComputersWork&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.2HowComputersWork.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.2HowComputersWork&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.2HowComputersWork.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./src/pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.js\");\n/* harmony import */ var _src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/lessons/level1/1.2HowComputersWork.js */ \"(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/lessons/level1/1.2HowComputersWork\",\n        pathname: \"/lessons/level1/1.2HowComputersWork\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_lessons_level1_1_2HowComputersWork_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.2HowComputersWork&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.2HowComputersWork.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/MarkAsReadButton.jsx":
/*!*********************************************!*\
  !*** ./src/components/MarkAsReadButton.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/progressTracker */ \"(pages-dir-node)/./src/utils/progressTracker.js\");\n\n\n\nconst MarkAsReadButton = ({ lessonId, level, onComplete, onNext })=>{\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedLessons, setCompletedLessons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkAsReadButton.useEffect\": ()=>{\n            // Check if lesson is already completed\n            const checkCompletion = {\n                \"MarkAsReadButton.useEffect.checkCompletion\": async ()=>{\n                    try {\n                        const progress = await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.getUserProgress)();\n                        setCompletedLessons(progress.completedLessons || []);\n                        setIsCompleted((0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.isLessonCompleted)(lessonId, progress.completedLessons || []));\n                    } catch (error) {\n                        console.error('Error checking lesson completion:', error);\n                    }\n                }\n            }[\"MarkAsReadButton.useEffect.checkCompletion\"];\n            checkCompletion();\n        }\n    }[\"MarkAsReadButton.useEffect\"], [\n        lessonId\n    ]);\n    const handleMarkAsRead = async ()=>{\n        if (isCompleted) return; // Already completed\n        setIsLoading(true);\n        try {\n            const updatedProgress = await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.markLessonComplete)(lessonId, level);\n            setIsCompleted(true);\n            setCompletedLessons(updatedProgress.completedLessons || []);\n            // Call onComplete callback if provided\n            if (onComplete) {\n                onComplete(updatedProgress);\n            }\n            // Show success message and navigate to next lesson\n            setTimeout(()=>{\n                if (onNext) {\n                    onNext(); // Navigate to next lesson\n                } else {\n                    alert('Lesson completed! 🎉');\n                }\n            }, 500); // Small delay to show the completed state\n        } catch (error) {\n            console.error('Error marking lesson as complete:', error);\n            alert('Failed to mark lesson as complete. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center mt-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleMarkAsRead,\n            disabled: isCompleted || isLoading,\n            className: `\n          px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105\n          ${isCompleted ? 'bg-green-500 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600 active:scale-95'}\n          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}\n        `,\n            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Updating...\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 69,\n                columnNumber: 11\n            }, undefined) : isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Completed\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 74,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                            lineNumber: 83,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Mark as Read\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 81,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarkAsReadButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL01hcmtBc1JlYWRCdXR0b24uanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBbUQ7QUFDK0M7QUFFbEcsTUFBTU0sbUJBQW1CLENBQUMsRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFO0lBQy9ELE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNZLFdBQVdDLGFBQWEsR0FBR2IsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDYyxrQkFBa0JDLG9CQUFvQixHQUFHZiwrQ0FBUUEsQ0FBQyxFQUFFO0lBRTNEQyxnREFBU0E7c0NBQUM7WUFDUix1Q0FBdUM7WUFDdkMsTUFBTWU7OERBQWtCO29CQUN0QixJQUFJO3dCQUNGLE1BQU1DLFdBQVcsTUFBTWIsdUVBQWVBO3dCQUN0Q1csb0JBQW9CRSxTQUFTSCxnQkFBZ0IsSUFBSSxFQUFFO3dCQUNuREgsZUFBZVIseUVBQWlCQSxDQUFDRyxVQUFVVyxTQUFTSCxnQkFBZ0IsSUFBSSxFQUFFO29CQUM1RSxFQUFFLE9BQU9JLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBcUNBO29CQUNyRDtnQkFDRjs7WUFFQUY7UUFDRjtxQ0FBRztRQUFDVjtLQUFTO0lBRWIsTUFBTWMsbUJBQW1CO1FBQ3ZCLElBQUlWLGFBQWEsUUFBUSxvQkFBb0I7UUFFN0NHLGFBQWE7UUFDYixJQUFJO1lBQ0YsTUFBTVEsa0JBQWtCLE1BQU1uQiwwRUFBa0JBLENBQUNJLFVBQVVDO1lBQzNESSxlQUFlO1lBQ2ZJLG9CQUFvQk0sZ0JBQWdCUCxnQkFBZ0IsSUFBSSxFQUFFO1lBRTFELHVDQUF1QztZQUN2QyxJQUFJTixZQUFZO2dCQUNkQSxXQUFXYTtZQUNiO1lBRUEsbURBQW1EO1lBQ25EQyxXQUFXO2dCQUNULElBQUliLFFBQVE7b0JBQ1ZBLFVBQVUsMEJBQTBCO2dCQUN0QyxPQUFPO29CQUNMYyxNQUFNO2dCQUNSO1lBQ0YsR0FBRyxNQUFNLDBDQUEwQztRQUNyRCxFQUFFLE9BQU9MLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkRLLE1BQU07UUFDUixTQUFVO1lBQ1JWLGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUNXO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNDO1lBQ0NDLFNBQVNQO1lBQ1RRLFVBQVVsQixlQUFlRTtZQUN6QmEsV0FBVyxDQUFDOztVQUVWLEVBQUVmLGNBQ0Usb0NBQ0EsZ0RBQ0g7VUFDRCxFQUFFRSxZQUFZLGtDQUFrQyxHQUFHO1FBQ3JELENBQUM7c0JBRUFBLDBCQUNDLDhEQUFDWTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNJO2tDQUFLOzs7Ozs7Ozs7Ozs0QkFFTm5CLDRCQUNGLDhEQUFDYztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNLO3dCQUFJTCxXQUFVO3dCQUFVTSxNQUFLO3dCQUFlQyxTQUFRO2tDQUNuRCw0RUFBQ0M7NEJBQUtDLFVBQVM7NEJBQVVDLEdBQUU7NEJBQXFIQyxVQUFTOzs7Ozs7Ozs7OztrQ0FFM0osOERBQUNQO2tDQUFLOzs7Ozs7Ozs7OzswQ0FHUiw4REFBQ0w7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSzt3QkFBSUwsV0FBVTt3QkFBVU0sTUFBSzt3QkFBT00sUUFBTzt3QkFBZUwsU0FBUTtrQ0FDakUsNEVBQUNDOzRCQUFLSyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0wsR0FBRTs7Ozs7Ozs7Ozs7a0NBRXZFLDhEQUFDTjtrQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1sQjtBQUVBLGlFQUFleEIsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9NYXJrQXNSZWFkQnV0dG9uLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1hcmtMZXNzb25Db21wbGV0ZSwgaXNMZXNzb25Db21wbGV0ZWQsIGdldFVzZXJQcm9ncmVzcyB9IGZyb20gJy4uL3V0aWxzL3Byb2dyZXNzVHJhY2tlcic7XG5cbmNvbnN0IE1hcmtBc1JlYWRCdXR0b24gPSAoeyBsZXNzb25JZCwgbGV2ZWwsIG9uQ29tcGxldGUsIG9uTmV4dCB9KSA9PiB7XG4gIGNvbnN0IFtpc0NvbXBsZXRlZCwgc2V0SXNDb21wbGV0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY29tcGxldGVkTGVzc29ucywgc2V0Q29tcGxldGVkTGVzc29uc10gPSB1c2VTdGF0ZShbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBpZiBsZXNzb24gaXMgYWxyZWFkeSBjb21wbGV0ZWRcbiAgICBjb25zdCBjaGVja0NvbXBsZXRpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwcm9ncmVzcyA9IGF3YWl0IGdldFVzZXJQcm9ncmVzcygpO1xuICAgICAgICBzZXRDb21wbGV0ZWRMZXNzb25zKHByb2dyZXNzLmNvbXBsZXRlZExlc3NvbnMgfHwgW10pO1xuICAgICAgICBzZXRJc0NvbXBsZXRlZChpc0xlc3NvbkNvbXBsZXRlZChsZXNzb25JZCwgcHJvZ3Jlc3MuY29tcGxldGVkTGVzc29ucyB8fCBbXSkpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgbGVzc29uIGNvbXBsZXRpb246JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBjaGVja0NvbXBsZXRpb24oKTtcbiAgfSwgW2xlc3NvbklkXSk7XG5cbiAgY29uc3QgaGFuZGxlTWFya0FzUmVhZCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoaXNDb21wbGV0ZWQpIHJldHVybjsgLy8gQWxyZWFkeSBjb21wbGV0ZWRcblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXBkYXRlZFByb2dyZXNzID0gYXdhaXQgbWFya0xlc3NvbkNvbXBsZXRlKGxlc3NvbklkLCBsZXZlbCk7XG4gICAgICBzZXRJc0NvbXBsZXRlZCh0cnVlKTtcbiAgICAgIHNldENvbXBsZXRlZExlc3NvbnModXBkYXRlZFByb2dyZXNzLmNvbXBsZXRlZExlc3NvbnMgfHwgW10pO1xuICAgICAgXG4gICAgICAvLyBDYWxsIG9uQ29tcGxldGUgY2FsbGJhY2sgaWYgcHJvdmlkZWRcbiAgICAgIGlmIChvbkNvbXBsZXRlKSB7XG4gICAgICAgIG9uQ29tcGxldGUodXBkYXRlZFByb2dyZXNzKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2UgYW5kIG5hdmlnYXRlIHRvIG5leHQgbGVzc29uXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKG9uTmV4dCkge1xuICAgICAgICAgIG9uTmV4dCgpOyAvLyBOYXZpZ2F0ZSB0byBuZXh0IGxlc3NvblxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFsZXJ0KCdMZXNzb24gY29tcGxldGVkISDwn46JJyk7XG4gICAgICAgIH1cbiAgICAgIH0sIDUwMCk7IC8vIFNtYWxsIGRlbGF5IHRvIHNob3cgdGhlIGNvbXBsZXRlZCBzdGF0ZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBtYXJraW5nIGxlc3NvbiBhcyBjb21wbGV0ZTonLCBlcnJvcik7XG4gICAgICBhbGVydCgnRmFpbGVkIHRvIG1hcmsgbGVzc29uIGFzIGNvbXBsZXRlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtdC02XCI+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZU1hcmtBc1JlYWR9XG4gICAgICAgIGRpc2FibGVkPXtpc0NvbXBsZXRlZCB8fCBpc0xvYWRpbmd9XG4gICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNVxuICAgICAgICAgICR7aXNDb21wbGV0ZWQgXG4gICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAgY3Vyc29yLW5vdC1hbGxvd2VkJyBcbiAgICAgICAgICAgIDogJ2JnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwIGFjdGl2ZTpzY2FsZS05NSdcbiAgICAgICAgICB9XG4gICAgICAgICAgJHtpc0xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9XG4gICAgICAgIGB9XG4gICAgICA+XG4gICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxuICAgICAgICAgICAgPHNwYW4+VXBkYXRpbmcuLi48L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiBpc0NvbXBsZXRlZCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDxzcGFuPkNvbXBsZXRlZDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk01IDEzbDQgNEwxOSA3XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPHNwYW4+TWFyayBhcyBSZWFkPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9idXR0b24+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBNYXJrQXNSZWFkQnV0dG9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtYXJrTGVzc29uQ29tcGxldGUiLCJpc0xlc3NvbkNvbXBsZXRlZCIsImdldFVzZXJQcm9ncmVzcyIsIk1hcmtBc1JlYWRCdXR0b24iLCJsZXNzb25JZCIsImxldmVsIiwib25Db21wbGV0ZSIsIm9uTmV4dCIsImlzQ29tcGxldGVkIiwic2V0SXNDb21wbGV0ZWQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJjb21wbGV0ZWRMZXNzb25zIiwic2V0Q29tcGxldGVkTGVzc29ucyIsImNoZWNrQ29tcGxldGlvbiIsInByb2dyZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlTWFya0FzUmVhZCIsInVwZGF0ZWRQcm9ncmVzcyIsInNldFRpbWVvdXQiLCJhbGVydCIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInNwYW4iLCJzdmciLCJmaWxsIiwidmlld0JveCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/MarkAsReadButton.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjs7QUFDYyxDQUFDLGdCQUFnQjtBQUNXLENBQUMsaUJBQWlCO0FBQ3pDO0FBRXBCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsd0JBQXdCO0FBQ3hCRixJQUFJRyxTQUFTLEdBQUc7SUFDZEYsV0FBV0YsK0RBQXFCLENBQUNNLFVBQVU7SUFDM0NILFdBQVdILDBEQUFnQixDQUFDTSxVQUFVO0FBQ3hDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLmpzXG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgXCIuL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3NcIjsgLy8gQW5pbWF0aW9ucyBDU1NcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSBcInByb3AtdHlwZXNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuLy8gQWRkIFByb3BUeXBlcyBmb3IgQXBwXG5BcHAucHJvcFR5cGVzID0ge1xuICBDb21wb25lbnQ6IFByb3BUeXBlcy5lbGVtZW50VHlwZS5pc1JlcXVpcmVkLCAvLyBDb21wb25lbnQgbXVzdCBiZSBhIHZhbGlkIFJlYWN0IGNvbXBvbmVudFxuICBwYWdlUHJvcHM6IFByb3BUeXBlcy5vYmplY3QuaXNSZXF1aXJlZCwgLy8gcGFnZVByb3BzIGlzIGFuIG9iamVjdFxufTtcbiJdLCJuYW1lcyI6WyJQcm9wVHlwZXMiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwcm9wVHlwZXMiLCJlbGVtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJvYmplY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZCAvPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css":
/*!******************************************************************!*\
  !*** ./src/pages/lessons/level-4/Section1/animations.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__VOjD6\",\n\t\"fadeInUp\": \"animations_fadeInUp__wib6A\",\n\t\"hidden\": \"animations_hidden__KLYnx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29uZmV0dGlcIjogXCJhbmltYXRpb25zX2NvbmZldHRpX19WT2pENlwiLFxuXHRcImZhZGVJblVwXCI6IFwiYW5pbWF0aW9uc19mYWRlSW5VcF9fd2liNkFcIixcblx0XCJoaWRkZW5cIjogXCJhbmltYXRpb25zX2hpZGRlbl9fS0xZbnhcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js":
/*!*********************************************************!*\
  !*** ./src/pages/lessons/level1/1.2HowComputersWork.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_MarkAsReadButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/MarkAsReadButton */ \"(pages-dir-node)/./src/components/MarkAsReadButton.jsx\");\n\n // Import Next.js Image component\n\n\nconst HowComputersWork = ({ onNext })=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        Input: \"\",\n        Storage: \"\",\n        Processing: \"\",\n        Output: \"\"\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const options = [\n        \"Processor\",\n        \"Keyboard\",\n        \"Monitor\",\n        \"Hard Drive\",\n        \"Meow🐱\"\n    ];\n    const correctAnswers = {\n        Input: \"Keyboard\",\n        Storage: \"Hard Drive\",\n        Processing: \"Processor\",\n        Output: \"Monitor\"\n    };\n    const handleMatch = (part, value)=>{\n        setMatches((prevMatches)=>({\n                ...prevMatches,\n                [part]: value\n            }));\n    };\n    const checkAnswers = ()=>{\n        const newFeedback = {};\n        let allCorrect = true;\n        for (const [part, correct] of Object.entries(correctAnswers)){\n            if (matches[part] === correct) {\n                newFeedback[part] = \"🌟 Correct!\";\n            } else {\n                newFeedback[part] = \"🚫 Try again!\";\n                allCorrect = false;\n            }\n        }\n        setFeedback(newFeedback);\n        setIsSubmitted(true);\n        if (allCorrect) {\n            alert(\"🎉 Great job! You matched all the parts correctly!\");\n        }\n    };\n    const resetActivity = ()=>{\n        setMatches({\n            Input: \"\",\n            Storage: \"\",\n            Processing: \"\",\n            Output: \"\"\n        });\n        setFeedback({});\n        setIsSubmitted(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-200 via-purple-50 to-pink-100 p-8 min-h-screen rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce\",\n                children: \"How Computers Work\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    src: \"/lvl1_img/a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg\",\n                    alt: \"Fun computer illustration\",\n                    width: 400,\n                    height: 250,\n                    className: \"rounded-lg shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mb-6 text-center\",\n                children: [\n                    \"A computer combines \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"input\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 29\n                    }, undefined),\n                    \", \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"storage\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 53\n                    }, undefined),\n                    \",\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"processing\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    \", and \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"output\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 80,\n                        columnNumber: 42\n                    }, undefined),\n                    \" to function.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-8 mb-8 text-lg text-purple-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Input:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Devices like a keyboard, mouse, or microphone give the computer information.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Memory/Storage:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Computers store your files on a hard drive or memory card.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Processing:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The processor, a microchip, processes the data with help from a cooling fan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Output:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Results are shown using devices like screens, speakers, or printers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-6 rounded-xl shadow-lg border-l-4 border-blue-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-center font-semibold text-blue-800 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Match the computer parts with their functions by selecting the correct option below!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-6\",\n                        children: [\n                            \"Input\",\n                            \"Storage\",\n                            \"Processing\",\n                            \"Output\"\n                        ].map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-xl shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4 text-purple-600\",\n                                        children: [\n                                            index + 1,\n                                            \". \",\n                                            part\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"p-3 border border-gray-300 rounded-xl bg-white shadow-inner w-full\",\n                                        value: matches[part],\n                                        onChange: (e)=>handleMatch(part, e.target.value),\n                                        disabled: isSubmitted,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option,\n                                                    children: option\n                                                }, option, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `mt-4 text-center font-bold ${feedback[part] === \"🌟 Correct!\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                        children: feedback[part]\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, part, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center gap-6\",\n                        children: !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-xl shadow-md hover:bg-purple-700 transition-all\",\n                            onClick: checkAnswers,\n                            children: \"Check Answers\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-yellow-500 text-white font-bold rounded-xl shadow-md hover:bg-yellow-600 transition-all\",\n                            onClick: resetActivity,\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkAsReadButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                lessonId: \"1.2HowComputersWork\",\n                level: \"level1\",\n                onNext: onNext,\n                onComplete: (progress)=>{\n                    console.log('Lesson completed!', progress);\n                }\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowComputersWork);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/progressTracker.js":
/*!**************************************!*\
  !*** ./src/utils/progressTracker.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallProgress: () => (/* binding */ calculateOverallProgress),\n/* harmony export */   getLessonDisplayName: () => (/* binding */ getLessonDisplayName),\n/* harmony export */   getNextLesson: () => (/* binding */ getNextLesson),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   isLessonCompleted: () => (/* binding */ isLessonCompleted),\n/* harmony export */   markLessonComplete: () => (/* binding */ markLessonComplete),\n/* harmony export */   trackLessonAccess: () => (/* binding */ trackLessonAccess),\n/* harmony export */   updateProgress: () => (/* binding */ updateProgress)\n/* harmony export */ });\n// Progress tracking utility for LMS\nconst API_BASE_URL =  false ? 0 : 'http://localhost:5000/api';\n// Get user's progress from backend\nconst getUserProgress = async ()=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage\n        if (!token) {\n            console.log('No token found, using local storage for progress');\n            return getProgressLocally();\n        }\n        const response = await fetch(`${API_BASE_URL}/progress`, {\n            method: 'GET',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return getProgressLocally();\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error fetching progress, using local storage fallback:', error.message);\n        return getProgressLocally();\n    }\n};\n// Get progress from local storage\nconst getProgressLocally = ()=>{\n    try {\n        const progress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        return {\n            currentLevel: progress.currentLevel || 'level1',\n            currentLesson: progress.currentLesson || '1.1WhatIsComputer',\n            completedLessons: progress.completedLessons || [],\n            accessedLessons: progress.accessedLessons || [],\n            lastAccessedAt: progress.lastAccessedAt || new Date().toISOString(),\n            totalLessonsCompleted: progress.totalLessonsCompleted || 0,\n            progressPercentage: progress.progressPercentage || 0\n        };\n    } catch (error) {\n        console.error('Error reading local progress:', error);\n        return {\n            currentLevel: 'level1',\n            currentLesson: '1.1WhatIsComputer',\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Local storage fallback for progress tracking\nconst updateProgressLocally = (lessonId, level, action)=>{\n    try {\n        // Get existing progress from localStorage\n        const existingProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        // Initialize if empty\n        if (!existingProgress.completedLessons) {\n            existingProgress.completedLessons = [];\n        }\n        if (!existingProgress.accessedLessons) {\n            existingProgress.accessedLessons = [];\n        }\n        // Update progress based on action\n        if (action === 'access' && !existingProgress.accessedLessons.includes(lessonId)) {\n            existingProgress.accessedLessons.push(lessonId);\n        }\n        if (action === 'complete' && !existingProgress.completedLessons.includes(lessonId)) {\n            existingProgress.completedLessons.push(lessonId);\n        }\n        // Update other fields\n        existingProgress.currentLevel = level;\n        existingProgress.currentLesson = lessonId;\n        existingProgress.lastAccessedAt = new Date().toISOString();\n        existingProgress.totalLessonsCompleted = existingProgress.completedLessons.length;\n        existingProgress.progressPercentage = calculateOverallProgress(existingProgress.completedLessons);\n        // Save back to localStorage\n        localStorage.setItem('userProgress', JSON.stringify(existingProgress));\n        console.log(`Progress updated locally: ${action} for ${lessonId} in ${level}`);\n        return existingProgress;\n    } catch (error) {\n        console.error('Error updating progress locally:', error);\n        return {\n            currentLevel: level,\n            currentLesson: lessonId,\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Update user's progress\nconst updateProgress = async (lessonId, level, action = 'access')=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage fallback\n        if (!token) {\n            console.log('No token found, using local storage for progress tracking');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const response = await fetch(`${API_BASE_URL}/progress/update`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                lessonId,\n                level,\n                action\n            })\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error updating progress, using local storage fallback:', error.message);\n        return updateProgressLocally(lessonId, level, action);\n    }\n};\n// Mark lesson as completed\nconst markLessonComplete = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'complete');\n};\n// Track lesson access\nconst trackLessonAccess = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'access');\n};\n// Check if lesson is completed\nconst isLessonCompleted = (lessonId, completedLessons)=>{\n    return completedLessons.includes(lessonId);\n};\n// Get next lesson based on current progress\nconst getNextLesson = (currentLevel, currentLesson)=>{\n    // Define lesson sequences for each level\n    const lessonSequences = {\n        level1: [\n            '1.1WhatIsComputer',\n            '1.2HowComputersWork',\n            '1.3WhatIsAProgram',\n            '1.4TypesOfComputers',\n            '1.5HardwareAndSoftware',\n            '1.6OperatingSystem',\n            '2.1WhatIsScratch',\n            '3.1WhatIsIoT',\n            '3.2HowIoTWorks',\n            '3.3IoTExamples',\n            '3.4IoTBenefits',\n            '4.1WhatIsComputerVision',\n            '4.2HowComputerVisionWorks',\n            '4.3ComputerVisionApplications',\n            '5.1Summary',\n            '5.3CompletionMessage'\n        ],\n        level2: [\n            '0.0CourseContent',\n            '0.1DisplayLevel2',\n            '01.0MetaverseAndAugmentedReality',\n            '01.1CoolTechnologyBehindMetaverse',\n            '02.01WhatIsAugmentedReality',\n            '02.0AugmentedReality',\n            '02.1MarkerBasedAR',\n            '02.2MarkerlessAR',\n            '02.3UsesOfAugmentedReality',\n            '03.0VirtualReality',\n            '03.2ApplicationsOfVirtualReality',\n            '04.1UnderstandingDNA',\n            '04.2CoolAIToolsForGenomics',\n            '04.3ApplicationsInMedicine',\n            '04.4FunFactsGenomicAI',\n            '2.0CyberSecurity',\n            '2.1WhatIsCybersecurity',\n            '2.2ThreatsAndAttacks',\n            '2.3ProtectingYourselfOnline',\n            '2.4FutureOfCybersecurity',\n            '3.0QuantumComputing',\n            '3.1WhatIsQuantumComputing',\n            '3.2QuantumBitsAndEntanglement',\n            '4.0GenomicAI'\n        ],\n        level3: [\n            '0.0CourseContent',\n            '1.0IntroductionToDataScience',\n            '1.1GlitchSaysHi',\n            '1.2WhatIsDataScience',\n            '1.3UnderstandingDataScience',\n            '2.1HowDataIsCollected',\n            '2.2UnderstandingMeanMedianMode',\n            '2.3ExploringDataTypes',\n            '3.0DataVisualization',\n            '3.1WhyVisualizeData',\n            '3.2CreateYourOwnBarGraph',\n            '3.3IntroductionToCharts',\n            '3.4DataStorytelling',\n            '3.5AdvancedVisualization',\n            '4.0Quiz',\n            '4.1Conclusion'\n        ],\n        level4: [\n            // Add level 4 lessons here\n            '1.1HelloWorld',\n            '1.2Pyramid',\n            '1.3Ascii',\n            '1.4Letter',\n            '2.1DataTypes',\n            '2.2DataTypes',\n            '2.3whew',\n            '3.1ControlFlow',\n            '3.2AreYouACatPerson',\n            '3.3TheGoodTheBadAndTheElif',\n            '3.4BookOfAnswers',\n            '3.5GodOfThunder',\n            '4.1DontMakeMeGuess',\n            '4.2OhYouAreActuallyMakingMeGuess',\n            '4.3PlierEtendreReleverElancer',\n            '4.4MyTrueLoveSentToMe',\n            '4.5GoodOlFizzBuzz'\n        ],\n        level5: [\n            // Add level 5 lessons here\n            'Ai',\n            'MachineLearning',\n            'NeuralNetworks',\n            'DeepLearning',\n            'Future',\n            'Final',\n            'Whatisnlp',\n            'Human',\n            'Breakingitdownwithtokenization',\n            'Cleaningupthemess',\n            'Letsgetsentimental',\n            'Buildingachatbot'\n        ]\n    };\n    const currentSequence = lessonSequences[currentLevel];\n    if (!currentSequence) return null;\n    const currentIndex = currentSequence.indexOf(currentLesson);\n    if (currentIndex === -1 || currentIndex === currentSequence.length - 1) {\n        // Move to next level if current level is complete\n        const levelNumber = parseInt(currentLevel.replace('level', ''));\n        const nextLevel = `level${levelNumber + 1}`;\n        if (lessonSequences[nextLevel]) {\n            return {\n                level: nextLevel,\n                lesson: lessonSequences[nextLevel][0]\n            };\n        }\n        return null; // All levels completed\n    }\n    return {\n        level: currentLevel,\n        lesson: currentSequence[currentIndex + 1]\n    };\n};\n// Calculate overall progress percentage\nconst calculateOverallProgress = (completedLessons)=>{\n    const totalLessons = 100; // Adjust based on actual total\n    return Math.round(completedLessons.length / totalLessons * 100);\n};\n// Get lesson display name\nconst getLessonDisplayName = (lessonId)=>{\n    const lessonNames = {\n        '1.1WhatIsComputer': 'What is a Computer?',\n        '1.2HowComputersWork': 'How Computers Work',\n        '1.3WhatIsAProgram': 'What is a Program?',\n        '1.4TypesOfComputers': 'Types of Computers',\n        '1.5HardwareAndSoftware': 'Hardware and Software',\n        '1.6OperatingSystem': 'Operating System',\n        '2.1WhatIsScratch': 'What is Scratch?',\n        '3.1WhatIsIoT': 'What is IoT?',\n        '3.2HowIoTWorks': 'How IoT Works',\n        '3.3IoTExamples': 'IoT Examples',\n        '3.4IoTBenefits': 'IoT Benefits',\n        '4.1WhatIsComputerVision': 'What is Computer Vision?',\n        '4.2HowComputerVisionWorks': 'How Computer Vision Works',\n        '4.3ComputerVisionApplications': 'Computer Vision Applications',\n        '5.1Summary': 'Summary',\n        '5.3CompletionMessage': 'Completion Message'\n    };\n    return lessonNames[lessonId] || lessonId;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/progressTracker.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.2HowComputersWork&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.2HowComputersWork.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();