import React, { useState } from "react";
import { useRouter } from "next/router";
import level1Data from "../../data/level1Data"; // Adjust the path if needed
import Spline from "@splinetool/react-spline"; // Import the Spline component

export default function Level1Lessons() {
  const router = useRouter();
  const [currentMajorTopic, setCurrentMajorTopic] = useState(null);
  const [currentMinorTopic, setCurrentMinorTopic] = useState(null);

  const handleMajorTopicClick = (majorIndex) => {
    setCurrentMajorTopic(majorIndex);
    setCurrentMinorTopic(0); // Automatically select the first sub-topic
  };

  const handleBackToMajorTopics = () => {
    setCurrentMajorTopic(null);
    setCurrentMinorTopic(null);
  };

  // Function to go to next lesson
  const goToNextLesson = () => {
    const currentMajor = level1Data.majorTopics[currentMajorTopic];
    const nextMinorIndex = currentMinorTopic + 1;

    if (nextMinorIndex < currentMajor.minorTopics.length) {
      // Next lesson in same major topic
      setCurrentMinorTopic(nextMinorIndex);
    } else {
      // Move to next major topic
      const nextMajorIndex = currentMajorTopic + 1;
      if (nextMajorIndex < level1Data.majorTopics.length) {
        setCurrentMajorTopic(nextMajorIndex);
        setCurrentMinorTopic(0);
      } else {
        // Course completed
        alert("🎉 Congratulations! You've completed Level 1!");
        router.push("/dashboard");
      }
    }
  };

  const currentTopic =
    currentMajorTopic !== null &&
    currentMinorTopic !== null &&
    level1Data.majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];
  const CurrentLessonComponent = currentTopic?.component;

  return (
    <div className="relative flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-1/4 bg-purple-700 text-white p-6 flex flex-col justify-between">
        <div>
          <h2 className="text-4xl font-extrabold mb-8 text-center drop-shadow-lg">
            Course Progress
          </h2>
          <ul className="space-y-4">
            {/* Major Topics */}
            {currentMajorTopic === null
              ? level1Data.majorTopics.map((major, majorIndex) => (
                  <li
                    key={majorIndex}
                    onClick={() => handleMajorTopicClick(majorIndex)}
                    className="text-lg font-semibold cursor-pointer hover:text-yellow-300 transition-all"
                  >
                    {major.title}
                  </li>
                ))
              : level1Data.majorTopics[currentMajorTopic]?.minorTopics.map(
                  (minor, minorIndex) => (
                    <li
                      key={minorIndex}
                      onClick={() => setCurrentMinorTopic(minorIndex)}
                      className={`text-lg font-semibold cursor-pointer ${
                        currentMinorTopic === minorIndex
                          ? "text-yellow-300"
                          : "hover:text-yellow-300"
                      } transition-all`}
                    >
                      {minor.title}
                    </li>
                  )
                )}
          </ul>
          {currentMajorTopic !== null && (
            <button
              onClick={handleBackToMajorTopics}
              className="mt-6 bg-yellow-500 text-black py-2 px-4 rounded-lg hover:bg-yellow-400 transition-all shadow-md"
            >
              Back to Topics
            </button>
          )}
        </div>

        {/* Back to Dashboard Button */}
        <button
          onClick={() => router.push('/dashboard')}
          className="mt-6 bg-green-500 text-white py-3 px-6 rounded-lg shadow-lg hover:bg-green-600 transition-all duration-300"
        >
          Back to Dashboard
        </button>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 relative bg-gradient-to-b from-blue-400 via-white to-green-600 flex flex-col items-center justify-center p-6">
        {/* Spline Model in the Background */}
        <div
          className="absolute inset-0 z-0"
          style={{
            width: "100%",
            height: "100%",
            pointerEvents: "none", // Disable interaction with the Spline model
          }}
        >
          <Spline
            scene="https://prod.spline.design/gUAXNK7hcAGZebnj/scene.splinecode"
            style={{
              width: "100%",
              height: "100%",
            }}
          />
        </div>

        {/* Content Area */}
        {currentMajorTopic === null && currentMinorTopic === null ? (
          <div className="relative z-10 text-center">
            <h1 className="text-6xl font-extrabold text-purple-700 mb-6 animate-bounce">
              Welcome Young Developer!
            </h1>
            <p className="text-3xl text-gray-700 mt-4 mb-6 leading-relaxed">
              Get ready to embark on an exciting learning journey! <br />
              Select a topic from the sidebar and dive into interactive lessons
              and activities.
            </p>
            <button
              onClick={() => handleMajorTopicClick(0)}
              className="mt-8 bg-purple-500 text-white py-3 px-8 rounded-full font-bold shadow-md hover:bg-purple-600 transition-all"
            >
              Let's Get Started!
            </button>
          </div>
        ) : (
          <div className="relative z-10 p-6 w-full">
            {CurrentLessonComponent ? (
              React.cloneElement(CurrentLessonComponent, { onNext: goToNextLesson })
            ) : (
              <p className="text-lg text-gray-700">
                No lesson found for the selected topic.
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
