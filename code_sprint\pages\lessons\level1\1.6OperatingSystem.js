import React, { useState } from "react";
import Image from "next/image";
import MarkAsReadButton from "../../../components/MarkAsReadButton";

const OperatingSystem = ({ onNext }) => {
  const [quizAnswer, setQuizAnswer] = useState("");
  const [quizFeedback, setQuizFeedback] = useState("");
  const [draggedItem, setDraggedItem] = useState(null);
  const [matches, setMatches] = useState({});
  const [dragFeedback, setDragFeedback] = useState("");

  const correctAnswer = "Windows";
  const options = ["Windows", "Google Chrome", "Microsoft Word", "Safari"];

  const correctMatches = {
    Desktop: "Windows",
    Mobile: "Android",
  };

  const handleQuizSubmit = () => {
    if (quizAnswer === correctAnswer) {
      setQuizFeedback("🎉 Correct! Windows is an operating system.");
    } else {
      setQuizFeedback("❌ Incorrect. Try again!");
    }
  };

  const handleDragStart = (item) => {
    setDraggedItem(item);
  };

  const handleDrop = (target) => {
    setMatches((prev) => ({ ...prev, [target]: draggedItem }));
    if (correctMatches[target] === draggedItem) {
      setDragFeedback(`🎉 Correct! ${draggedItem} is the right match for ${target}.`);
    } else {
      setDragFeedback(`❌ Incorrect. ${draggedItem} does not match with ${target}.`);
    }
    setDraggedItem(null);
  };

  return (
    <div className="text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50">
      <h1 className="text-4xl font-bold mb-6 text-purple-700 text-center">
        What is an Operating System?
      </h1>

      {/* Image Section */}
      <div className="flex justify-center mb-6">
        <Image
          src="/lvl1_img/Untitled (6).png" // Replace with your image path
          alt="Operating System Illustration"
          width={500}
          height={300}
          className="rounded-lg shadow-md"
        />
      </div>

      <p className="text-lg mb-4 text-center">
        An <strong>Operating System (OS)</strong> is like the boss of a computer. It tells all the parts of the computer how to work together. Without an OS, your computer wouldn’t know how to show your games, apps, or even turn on properly!
      </p>
      <div className="bg-blue-100 p-4 border-l-4 border-blue-500 mb-4 rounded-lg">
        <p>
          <strong>Example:</strong> Imagine you have a robot that can dance, sing, and clean. The operating system is like the robot’s brain—it gives instructions so everything works smoothly.
        </p>
      </div>
      <p className="text-lg mb-4">
        There are many types of operating systems:
      </p>
      <ul className="list-disc pl-6 text-lg mb-6">
        <li>
          <strong>Windows:</strong> A popular OS for laptops and desktop computers.
        </li>
        <li>
          <strong>MacOS:</strong> The OS used by Apple computers.
        </li>
        <li>
          <strong>Android:</strong> Used in most smartphones and tablets.
        </li>
        <li>
          <strong>iOS:</strong> The OS for iPhones and iPads.
        </li>
      </ul>

      <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg">
        <p>
          <strong>Fun Fact:</strong> Without an operating system, your computer would be like a car without a driver—it has all the parts but doesn’t know how to move or what to do!
        </p>
      </div>

      {/* Quiz Activity */}
      <div className="bg-green-100 p-4 border-l-4 border-green-500 mb-6 rounded-lg shadow-lg">
        <p>
          <strong>Activity 1:</strong> Which of the following is an operating system?
        </p>
        <div className="mt-4">
          {options.map((option, index) => (
            <div key={index} className="mb-2">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="quiz"
                  value={option}
                  onChange={(e) => setQuizAnswer(e.target.value)}
                />
                <span>{option}</span>
              </label>
            </div>
          ))}
        </div>
        <button
          onClick={handleQuizSubmit}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Submit Answer
        </button>
        {quizFeedback && <p className="mt-4 text-lg font-bold">{quizFeedback}</p>}
      </div>

      {/* Drag-and-Drop Activity */}
      <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-lg">
        <p className="text-xl font-semibold">
          <strong>Activity 2:</strong> Match the operating systems to the devices they work best on.
        </p>
        <div className="flex flex-row mt-6 space-x-6 justify-center">
          {/* Draggable Items */}
          <div className="flex flex-col items-center space-y-4">
            <div
              draggable
              onDragStart={() => handleDragStart("Windows")}
              className="w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50"
            >
              Windows
            </div>
            <div
              draggable
              onDragStart={() => handleDragStart("Android")}
              className="w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50"
            >
              Android
            </div>
          </div>

          {/* Drop Zones */}
          <div className="flex flex-col items-center space-y-4">
            <div
              onDrop={() => handleDrop("Desktop")}
              onDragOver={(e) => e.preventDefault()}
              className="w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md"
            >
              {matches.Desktop || "Desktop"}
            </div>
            <div
              onDrop={() => handleDrop("Mobile")}
              onDragOver={(e) => e.preventDefault()}
              className="w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md"
            >
              {matches.Mobile || "Mobile"}
            </div>
          </div>
        </div>
        {dragFeedback && <p className="mt-4 text-lg font-bold">{dragFeedback}</p>}
      </div>

      {/* Mark as Read Button */}
      <MarkAsReadButton
        lessonId="1.6OperatingSystem"
        level="level1"
        onNext={onNext}
        onComplete={(progress) => {
          console.log('Lesson completed!', progress);
        }}
      />
    </div>
  );
};

export default OperatingSystem;
